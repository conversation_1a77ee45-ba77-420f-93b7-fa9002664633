{"name": "boilerplate-nextjs15", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.7.9", "next": "15.1.7", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.24.1", "eslint": "^9", "eslint-config-next": "15.1.7", "eslint-plugin-prettier": "^5.2.3", "postcss": "^8", "prettier": "^3.5.2", "tailwindcss": "^3.4.1", "typescript": "^5"}}