import type { Metada<PERSON> } from 'next';
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import '@/styles/globals.css';
import Header from '@/components/layouts/header';
import Footer from '@/components/layouts/footer';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'BOILER',
  description: 'BOILER PLATE',
};

export default function RootLayout({
  modals,
  children,
}: Readonly<{
  modals: React.ReactNode;
  children: React.ReactNode;
}>) {
  return (
    <html lang="ko">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {modals}
        <Header />
        {children}
        <Footer />
      </body>
    </html>
  );
}
