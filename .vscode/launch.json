{"version": "0.2.0", "configurations": [{"name": "Next.js (Turbopack Dev Server)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev", "--turbo"], "port": 3000, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "development", "NEXT_TURBOPACK": "1"}}, {"name": "Next.js (Turbopack Attach Debugger)", "type": "node", "request": "attach", "port": 9229, "restart": true, "timeout": 10000}, {"name": "Next.js Build & Start", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "build"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "production"}}]}